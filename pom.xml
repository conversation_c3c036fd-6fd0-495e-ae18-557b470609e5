<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!--
        版本号升级
        mvn versions:set -DnewVersion=1.0
        mvn versions:commit
    -->
    <groupId>com.dc</groupId>
    <artifactId>summer</artifactId>
    <packaging>pom</packaging>
    <version>1.0</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <global.spring-boot.version>2.7.18</global.spring-boot.version>
        <slf4j.version>1.7.36</slf4j.version>
        <junit.version>5.8.2</junit.version>
        <jedis.version>4.3.2</jedis.version>
        <redisson.version>3.17.7</redisson.version>
        <zip4j.version>1.3.2</zip4j.version>
        <tika.version>1.28.3</tika.version>
        <mysql.version>8.2.0</mysql.version>
        <swagger.version>3.0.0</swagger.version>
        <poi.version>5.2.3</poi.version>
        <druid.version>1.2.24</druid.version>
        <mongodb.version>4.6.1</mongodb.version>
        <elasticsearch.version>8.12.2</elasticsearch.version>
        <hbase.version>2.2.3</hbase.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <netty.version>4.1.108.Final</netty.version>
        <protobuf.version>4.26.1</protobuf.version>
        <antlr4.version>4.13.1</antlr4.version>
        <guava.version>33.2.0-jre</guava.version>
        <bouncycastle.version>1.78.1</bouncycastle.version>
        <lombok.version>1.18.30</lombok.version>
        <caffeine.version>2.9.3</caffeine.version>
        <bridge.version>enterprise5.2.0.37.56b3f5a</bridge.version>
        <commons-net.version>3.11.1</commons-net.version>
    </properties>

    <modules>
        <module>base</module>
        <module>modules</module>
        <module>product</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- springboot 使用新版本，但是配置使用老版本，以便能够兼容 neo4j -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${global.spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.6</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2021.0.6.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 锁定新版本的 redis ：默认 springboot 会使用旧版本的 jedis 包。 -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!-- eclipse 可变 -->
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.equinox.app -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.equinox.app</artifactId>
                <version>1.6.200</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.net -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.net</artifactId>
                <version>1.3.1200</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.resources -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.resources</artifactId>
                <version>3.17.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.expressions -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.expressions</artifactId>
                <version>3.8.200</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.text -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.text</artifactId>
                <version>3.12.100</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.runtime -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.runtime</artifactId>
                <version>3.26.100</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.contenttype -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.contenttype</artifactId>
                <version>3.8.200</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.filesystem -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.filesystem</artifactId>
                <version>1.9.500</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.equinox.registry -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.equinox.registry</artifactId>
                <version>3.11.200</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.commands -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.commands</artifactId>
                <version>3.10.200</version>
            </dependency>

            <!-- eclipse 不变 -->
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.equinox.preferences -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.equinox.preferences</artifactId>
                <version>3.10.200</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.core.jobs -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.core.jobs</artifactId>
                <version>3.13.300</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.equinox.common -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.equinox.common</artifactId>
                <version>3.17.100</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.osgi -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.osgi</artifactId>
                <version>3.18.300</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.eclipse.platform/org.eclipse.equinox.security -->
            <dependency>
                <groupId>org.eclipse.platform</groupId>
                <artifactId>org.eclipse.equinox.security</artifactId>
                <version>1.3.900</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.osgi/org.osgi.service.event -->
            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>org.osgi.service.event</artifactId>
                <version>1.4.1</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.github.jsqlparser/jsqlparser -->
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>4.6</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>com.intellij</groupId>
            <artifactId>annotations</artifactId>
            <version>12.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-jexl3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
            <version>3.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.locationtech.jts/jts-core -->
        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>1.18.0</version>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>3.3.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mapstruct/mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15to18 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons-net.version}</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>run-once</id>
            <activation>
                <file>
                    <exists>GenerateAntlr4.java</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <!-- 下的远程仓库的 g4 文件 -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>compile-and-run</id>
                                <phase>initialize</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>javac</executable> <!-- 编译 Java 文件 -->
                                    <arguments>
                                        <argument>GenerateAntlr4.java</argument>
                                        <argument>-encoding</argument>
                                        <argument>UTF-8</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>run-script</id>
                                <phase>initialize</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>java</executable> <!-- 运行 Java 文件 -->
                                    <arguments>
                                        <argument>GenerateAntlr4</argument>
                                        <argument>${antlr4.branch}</argument> <!--默认 master-->
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>

        <plugins>
            <!-- 先清理antlr4的java代码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>src/main/java/${artifactId}.parser.autogen</directory>
                        </fileset>
                        <fileset>
                            <directory>gen</directory>
                        </fileset>
                        <fileset>
                            <directory>src/main/resources/antlr4/imports/gen</directory>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <!-- 再生成antlr4的java代码 -->
            <plugin>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-maven-plugin</artifactId>
                <version>${antlr4.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>antlr4</goal>
                        </goals>
                        <configuration>
                            <arguments>
                                <argument>-package</argument>
                                <argument>${artifactId}.parser.autogen</argument>
                            </arguments>
                            <sourceDirectory>src/main/resources/antlr4</sourceDirectory>
                            <libDirectory>src/main/resources/antlr4/imports/</libDirectory>
                            <outputDirectory>src/main/java/${artifactId}.parser.autogen</outputDirectory>
                            <listener>true</listener>
                            <visitor>true</visitor>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                </includes>
            </resource>

            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>antlr4/**</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

</project>
