package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowMasterStatusStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show master status statement context.
 */
@Getter
public final class ShowMasterStatusStatementContext extends CommonSQLStatementContext {

    public ShowMasterStatusStatementContext(final ShowMasterStatusStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show master status statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowMasterStatusStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有MASTER_STATUS常量，创建变量用于引用
        String masterStatusType = "MASTER_STATUS";
        sqlAuthModel.setType(masterStatusType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowMasterStatusStatement getSqlStatement() {
        return (ShowMasterStatusStatement) super.getSqlStatement();
    }
}
