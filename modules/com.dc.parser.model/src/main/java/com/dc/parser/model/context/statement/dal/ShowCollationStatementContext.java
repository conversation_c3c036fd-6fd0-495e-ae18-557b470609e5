package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCollationStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show collation statement context.
 */
@Getter
public final class ShowCollationStatementContext extends CommonSQLStatementContext {

    public ShowCollationStatementContext(final ShowCollationStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show collation statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCollationStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有COLLATION常量，创建变量用于引用
        String collationType = "COLLATION";
        sqlAuthModel.setType(collationType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCollationStatement getSqlStatement() {
        return (ShowCollationStatement) super.getSqlStatement();
    }
}
