package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowWarningsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show warnings statement context.
 */
@Getter
public final class ShowWarningsStatementContext extends CommonSQLStatementContext {

    public ShowWarningsStatementContext(final ShowWarningsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show warnings statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowWarningsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有WARNING常量，创建变量用于引用
        String warningType = "WARNING";
        sqlAuthModel.setType(warningType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowWarningsStatement getSqlStatement() {
        return (ShowWarningsStatement) super.getSqlStatement();
    }
}
