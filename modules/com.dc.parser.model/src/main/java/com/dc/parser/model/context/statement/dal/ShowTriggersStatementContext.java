package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowTriggersStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show triggers statement context.
 */
@Getter
public final class ShowTriggersStatementContext extends CommonSQLStatementContext {

    public ShowTriggersStatementContext(final ShowTriggersStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show triggers statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowTriggersStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_TRIGGER);
        
        // 如果指定了数据库，使用指定的数据库名，否则使用当前数据库名
        String schemaName = currentDatabaseName;
        if (sqlStatement.getFromDatabase().isPresent()) {
            schemaName = sqlStatement.getFromDatabase().get().getDatabase().getIdentifier().getValue();
        }
        sqlAuthModel.setSchemaName(schemaName);
        
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowTriggersStatement getSqlStatement() {
        return (ShowTriggersStatement) super.getSqlStatement();
    }
}
