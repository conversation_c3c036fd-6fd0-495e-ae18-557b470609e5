package com.dc.parser.model.metadata.database.schema;

import com.dc.parser.model.metadata.model.ColumnMetaData;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * ShardingSphere column.
 */
@RequiredArgsConstructor
@Getter
@ToString
public final class ShardingSphereColumn {

    private final String name;

    private final int dataType;

    private final boolean primaryKey;

    private final boolean generated;

    private final boolean caseSensitive;

    private final boolean visible;

    private final boolean unsigned;

    private final boolean nullable;

    private final String comment;

    public ShardingSphereColumn(final ColumnMetaData columnMetaData) {
        name = columnMetaData.getName();
        dataType = columnMetaData.getDataType();
        primaryKey = columnMetaData.isPrimaryKey();
        generated = columnMetaData.isGenerated();
        caseSensitive = columnMetaData.isCaseSensitive();
        visible = columnMetaData.isVisible();
        unsigned = columnMetaData.isUnsigned();
        nullable = columnMetaData.isNullable();
        comment = columnMetaData.getComment();
    }
}
