package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowEngineStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show engine statement context.
 */
@Getter
public final class ShowEngineStatementContext extends CommonSQLStatementContext {

    public ShowEngineStatementContext(final ShowEngineStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show engine statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowEngineStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有ENGINE常量，创建变量用于引用
        String engineType = "ENGINE";
        sqlAuthModel.setType(engineType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getEngineName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowEngineStatement getSqlStatement() {
        return (ShowEngineStatement) super.getSqlStatement();
    }
}
