package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCreateFunctionStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show create function statement context.
 */
@Getter
public final class ShowCreateFunctionStatementContext extends CommonSQLStatementContext {

    public ShowCreateFunctionStatementContext(final ShowCreateFunctionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show create function statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCreateFunctionStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getFunctionName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCreateFunctionStatement getSqlStatement() {
        return (ShowCreateFunctionStatement) super.getSqlStatement();
    }
}
