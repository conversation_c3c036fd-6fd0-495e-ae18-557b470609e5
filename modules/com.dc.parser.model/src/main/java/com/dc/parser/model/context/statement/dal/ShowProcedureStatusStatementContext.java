package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowProcedureStatusStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show procedure status statement context.
 */
@Getter
public final class ShowProcedureStatusStatementContext extends CommonSQLStatementContext {

    public ShowProcedureStatusStatementContext(final ShowProcedureStatusStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show procedure status statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowProcedureStatusStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.PROCEDURE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowProcedureStatusStatement getSqlStatement() {
        return (ShowProcedureStatusStatement) super.getSqlStatement();
    }
}
