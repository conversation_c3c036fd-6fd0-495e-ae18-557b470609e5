package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCharacterSetStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show character set statement context.
 */
@Getter
public final class ShowCharacterSetStatementContext extends CommonSQLStatementContext {

    public ShowCharacterSetStatementContext(final ShowCharacterSetStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show character set statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCharacterSetStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有CHARACTER_SET常量，创建变量用于引用
        String characterSetType = "CHARACTER_SET";
        sqlAuthModel.setType(characterSetType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCharacterSetStatement getSqlStatement() {
        return (ShowCharacterSetStatement) super.getSqlStatement();
    }
}
