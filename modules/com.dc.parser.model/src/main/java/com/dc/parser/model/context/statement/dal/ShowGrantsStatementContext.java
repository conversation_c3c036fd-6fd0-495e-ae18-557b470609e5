package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowGrantsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show grants statement context.
 */
@Getter
public final class ShowGrantsStatementContext extends CommonSQLStatementContext {

    public ShowGrantsStatementContext(final ShowGrantsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show grants statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowGrantsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_GRANT);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowGrantsStatement getSqlStatement() {
        return (ShowGrantsStatement) super.getSqlStatement();
    }
}
