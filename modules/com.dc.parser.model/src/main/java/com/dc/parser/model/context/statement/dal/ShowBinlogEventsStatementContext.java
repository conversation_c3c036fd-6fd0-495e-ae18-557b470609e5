package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowBinlogEventsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show binlog events statement context.
 */
@Getter
public final class ShowBinlogEventsStatementContext extends CommonSQLStatementContext {

    public ShowBinlogEventsStatementContext(final ShowBinlogEventsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show binlog events statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowBinlogEventsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_BINLOG);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowBinlogEventsStatement getSqlStatement() {
        return (ShowBinlogEventsStatement) super.getSqlStatement();
    }
}
