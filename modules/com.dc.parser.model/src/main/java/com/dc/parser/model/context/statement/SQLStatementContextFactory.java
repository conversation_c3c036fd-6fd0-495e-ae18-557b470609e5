package com.dc.parser.model.context.statement;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.statement.dal.*;
import com.dc.parser.model.context.statement.dcl.*;
import com.dc.parser.model.context.statement.ddl.*;
import com.dc.parser.model.context.statement.dml.*;
import com.dc.parser.model.context.statement.tcl.*;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dal.*;
import com.dc.parser.model.statement.dcl.*;
import com.dc.parser.model.statement.ddl.*;
import com.dc.parser.model.statement.dml.*;
import com.dc.parser.model.statement.tcl.*;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * SQL statement context factory.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SQLStatementContextFactory {

    /**
     * Create SQL statement context.
     *
     * @param sqlStatement        SQL statement
     * @param params              SQL parameters
     * @param currentDatabaseName current database name
     * @param sqlAuthModels       SQL authorization models
     * @param sqlActionModel      SQL action model
     * @return SQL statement context
     */
    public static SQLStatementContext newInstance(final SQLStatement sqlStatement, final List<Object> params, final String currentDatabaseName, List<SqlAuthModel> sqlAuthModels, final SqlActionModel sqlActionModel) {
        SQLStatementContext context = null;
        if (sqlStatement instanceof DMLStatement) {
            context = getDMLStatementContext((DMLStatement) sqlStatement, params, currentDatabaseName);
        }
        if (sqlStatement instanceof DDLStatement) {
            context = getDDLStatementContext((DDLStatement) sqlStatement, params, currentDatabaseName);
        }
        if (sqlStatement instanceof DCLStatement) {
            context = getDCLStatementContext((DCLStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof TCLStatement) {
            context = getTCLStatementContext((TCLStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DALStatement) {
            context = getDALStatementContext((DALStatement) sqlStatement, currentDatabaseName);
        }

        if (context != null) {
            if (CollectionUtils.isNotEmpty(sqlAuthModels)) {
                context.getSqlAuthModels().addAll(0, sqlAuthModels);
            }
            context.setSqlActionModel(SqlActionModel.merge(context.getSqlActionModel(), sqlActionModel));
            return context;
        }
        return new UnknownSQLStatementContext(sqlStatement);
    }

    private static SQLStatementContext getDMLStatementContext(final DMLStatement sqlStatement, final List<Object> params, final String currentDatabaseName) {
        if (sqlStatement instanceof SelectStatement) {
            return new SelectStatementContext(params, (SelectStatement) sqlStatement, currentDatabaseName, Collections.emptyList());
        }
        if (sqlStatement instanceof UpdateStatement) {
            return new UpdateStatementContext((UpdateStatement) sqlStatement);
        }
        if (sqlStatement instanceof DeleteStatement) {
            return new DeleteStatementContext((DeleteStatement) sqlStatement);
        }
        if (sqlStatement instanceof InsertStatement) {
            return new InsertStatementContext(params, (InsertStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UpsertStatement) {
            return new UpsertStatementContext((UpsertStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CopyStatement) {
            return new CopyStatementContext((CopyStatement) sqlStatement);
        }
        if (sqlStatement instanceof LoadDataStatement) {
            return new LoadDataStatementContext((LoadDataStatement) sqlStatement);
        }
        if (sqlStatement instanceof LoadXMLStatement) {
            return new LoadXMLStatementContext((LoadXMLStatement) sqlStatement);
        }
        if (sqlStatement instanceof LockTableStatement) {
            return new LockTableStatementContext((LockTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof MergeStatement) {
            return new MergeStatementContext((MergeStatement) sqlStatement);
        }
        if (sqlStatement instanceof CallStatement) {
            return new CallStatementContext((CallStatement) sqlStatement);
        }
        if (sqlStatement instanceof CloseCursorStatement) {
            return new CloseCursorStatementContext((CloseCursorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DeallocateCursorStatement) {
            return new DeallocateCursorStatementContext((DeallocateCursorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof FetchCursorStatement) {
            return new FetchCursorStatementContext((FetchCursorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof OpenCursorStatement) {
            return new OpenCursorStatementContext((OpenCursorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof PrintStatement) {
            return new PrintStatementContext((PrintStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RaiseerrorStatement) {
            return new RaiseerrorStatementContext((RaiseerrorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ReceiveStatement) {
            return new ReceiveStatementContext((ReceiveStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SendConversationStatement) {
            return new SendConversationStatementContext((SendConversationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CopyTestcaseStatement) {
            return new CopyTestcaseStatementContext((CopyTestcaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ValuesStatement) {
            return new ValuesStatementContext((ValuesStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof LoadStatementStatement) {
            return new LoadStatementStatementContext((LoadStatementStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MergeDeltaStatement) {
            return new MergeDeltaStatementContext((MergeDeltaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AllocateRowStatement) {
            return new AllocateRowStatementContext((AllocateRowStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof PutStatement) {
            return new PutStatementContext((PutStatement) sqlStatement, currentDatabaseName);
        }
        return new UnknownSQLStatementContext(sqlStatement);
    }

    private static SQLStatementContext getDDLStatementContext(final DDLStatement sqlStatement, final List<Object> params, final String currentDatabaseName) {
        if (sqlStatement instanceof CreateSchemaStatement) {
            return new CreateSchemaStatementContext((CreateSchemaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTableStatement) {
            return new CreateTableStatementContext((CreateTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof AlterTableStatement) {
            return new AlterTableStatementContext((AlterTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof RenameTableStatement) {
            return new RenameTableStatementContext((RenameTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof DropTableStatement) {
            return new DropTableStatementContext((DropTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof CreateIndexStatement) {
            return new CreateIndexStatementContext((CreateIndexStatement) sqlStatement);
        }
        if (sqlStatement instanceof AlterIndexStatement) {
            return new AlterIndexStatementContext((AlterIndexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropIndexStatement) {
            return new DropIndexStatementContext((DropIndexStatement) sqlStatement);
        }
        if (sqlStatement instanceof TruncateStatement) {
            return new TruncateStatementContext((TruncateStatement) sqlStatement);
        }
        if (sqlStatement instanceof CreateFunctionStatement) {
            return new CreateFunctionStatementContext((CreateFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateProcedureStatement) {
            return new CreateProcedureStatementContext((CreateProcedureStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateViewStatement) {
            return new CreateViewStatementContext(params, (CreateViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterViewStatement) {
            return new AlterViewStatementContext(params, (AlterViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropViewStatement) {
            return new DropViewStatementContext((DropViewStatement) sqlStatement);
        }
        if (sqlStatement instanceof PrepareStatement) {
            return new PrepareStatementContext((PrepareStatement) sqlStatement);
        }
        if (sqlStatement instanceof CommentStatement) {
            return new CommentStatementContext((CommentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDatabaseStatement) {
            return new CreateDatabaseStatementContext((CreateDatabaseStatement) sqlStatement);
        }
        if (sqlStatement instanceof DropDatabaseStatement) {
            return new DropDatabaseStatementContext((DropDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTablespaceStatement) {
            return new CreateTablespaceStatementContext((CreateTablespaceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDatabaseLinkStatement) {
            return new CreateDatabaseLinkStatementContext((CreateDatabaseLinkStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDatabaseLinkStatement) {
            return new DropDatabaseLinkStatementContext((DropDatabaseLinkStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropProcedureStatement) {
            return new DropProcedureStatementContext((DropProcedureStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterTriggerStatement) {
            return new AlterTriggerStatementContext((AlterTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMaterializedViewStatement) {
            return new CreateMaterializedViewStatementContext((CreateMaterializedViewStatement) sqlStatement);
        }
        if (sqlStatement instanceof DropMaterializedViewStatement) {
            return new DropMaterializedViewStatementContext((DropMaterializedViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMaterializedViewStatement) {
            return new AlterMaterializedViewStatementContext((AlterMaterializedViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAggregateStatement) {
            return new AlterAggregateStatementContext((AlterAggregateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterCollationStatement) {
            return new AlterCollationStatementContext((AlterCollationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterConversionStatement) {
            return new AlterConversionStatementContext((AlterConversionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDefaultPrivilegesStatement) {
            return new AlterDefaultPrivilegesStatementContext((AlterDefaultPrivilegesStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDomainStatement) {
            return new AlterDomainStatementContext((AlterDomainStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterExtensionStatement) {
            return new AlterExtensionStatementContext((AlterExtensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterForeignDataWrapperStatement) {
            return new AlterForeignDataWrapperStatementContext((AlterForeignDataWrapperStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterForeignTableStatement) {
            return new AlterForeignTableStatementContext((AlterForeignTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterEventTriggerStatement) {
            return new AlterEventTriggerStatementContext((AlterEventTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterGroupStatement) {
            return new AlterGroupStatementContext((AlterGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterLanguageStatement) {
            return new AlterLanguageStatementContext((AlterLanguageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterLargeObjectStatement) {
            return new AlterLargeObjectStatementContext((AlterLargeObjectStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterOperatorStatement) {
            return new AlterOperatorStatementContext((AlterOperatorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPolicyStatement) {
            return new AlterPolicyStatementContext((AlterPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPublicationStatement) {
            return new AlterPublicationStatementContext((AlterPublicationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRoutineStatement) {
            return new AlterRoutineStatementContext((AlterRoutineStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRuleStatement) {
            return new AlterRuleStatementContext((AlterRuleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServerStatement) {
            return new AlterServerStatementContext((AlterServerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterStatisticsStatement) {
            return new AlterStatisticsStatementContext((AlterStatisticsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSubscriptionStatement) {
            return new AlterSubscriptionStatementContext((AlterSubscriptionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSystemStatement) {
            return new AlterSystemStatementContext((AlterSystemStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterTypeStatement) {
            return new AlterTypeStatementContext((AlterTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterTextSearchStatement) {
            return new AlterTextSearchStatementContext((AlterTextSearchStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterUserMappingStatement) {
            return new AlterUserMappingStatementContext((AlterUserMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CloseStatement) {
            return new CloseStatementContext((CloseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ClusterStatement) {
            return new ClusterStatementContext((ClusterStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAccessMethodStatement) {
            return new CreateAccessMethodStatementContext((CreateAccessMethodStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAggregateStatement) {
            return new CreateAggregateStatementContext((CreateAggregateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateCastStatement) {
            return new CreateCastStatementContext((CreateCastStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateCollationStatement) {
            return new CreateCollationStatementContext((CreateCollationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateConversionStatement) {
            return new CreateConversionStatementContext((CreateConversionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDomainStatement) {
            return new CreateDomainStatementContext((CreateDomainStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEventTriggerStatement) {
            return new CreateEventTriggerStatementContext((CreateEventTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExtensionStatement) {
            return new CreateExtensionStatementContext((CreateExtensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateForeignDataWrapperStatement) {
            return new CreateForeignDataWrapperStatementContext((CreateForeignDataWrapperStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateForeignTableStatement) {
            return new CreateForeignTableStatementContext((CreateForeignTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateLanguageStatement) {
            return new CreateLanguageStatementContext((CreateLanguageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateOperatorStatement) {
            return new CreateOperatorStatementContext((CreateOperatorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePolicyStatement) {
            return new CreatePolicyStatementContext((CreatePolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePublicationStatement) {
            return new CreatePublicationStatementContext((CreatePublicationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRuleStatement) {
            return new CreateRuleStatementContext((CreateRuleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTextSearchStatement) {
            return new CreateTextSearchStatementContext((CreateTextSearchStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTransformStatement) {
            return new CreateTransformStatementContext((CreateTransformStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateStatisticsStatement) {
            return new CreateStatisticsStatementContext((CreateStatisticsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateUserMappingStatement) {
            return new CreateUserMappingStatementContext((CreateUserMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSubscriptionStatement) {
            return new CreateSubscriptionStatementContext((CreateSubscriptionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DeallocateStatement) {
            return new DeallocateStatementContext((DeallocateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DeclareStatement) {
            return new DeclareStatementContext((DeclareStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DiscardStatement) {
            return new DiscardStatementContext((DiscardStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropAccessMethodStatement) {
            return new DropAccessMethodStatementContext((DropAccessMethodStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropAggregateStatement) {
            return new DropAggregateStatementContext((DropAggregateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropCastStatement) {
            return new DropCastStatementContext((DropCastStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropCollationStatement) {
            return new DropCollationStatementContext((DropCollationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropConversionStatement) {
            return new DropConversionStatementContext((DropConversionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDomainStatement) {
            return new DropDomainStatementContext((DropDomainStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropEventTriggerStatement) {
            return new DropEventTriggerStatementContext((DropEventTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropExtensionStatement) {
            return new DropExtensionStatementContext((DropExtensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropForeignDataWrapperStatement) {
            return new DropForeignDataWrapperStatementContext((DropForeignDataWrapperStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropForeignTableStatement) {
            return new DropForeignTableStatementContext((DropForeignTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropGroupStatement) {
            return new DropGroupStatementContext((DropGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropLanguageStatement) {
            return new DropLanguageStatementContext((DropLanguageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropOperatorClassStatement) {
            return new DropOperatorClassStatementContext((DropOperatorClassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropOperatorFamilyStatement) {
            return new DropOperatorFamilyStatementContext((DropOperatorFamilyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropOperatorStatement) {
            return new DropOperatorStatementContext((DropOperatorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropOwnedStatement) {
            return new DropOwnedStatementContext((DropOwnedStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPolicyStatement) {
            return new DropPolicyStatementContext((DropPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPublicationStatement) {
            return new DropPublicationStatementContext((DropPublicationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRoutineStatement) {
            return new DropRoutineStatementContext((DropRoutineStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRuleStatement) {
            return new DropRuleStatementContext((DropRuleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSchemaStatement) {
            return new DropSchemaStatementContext((DropSchemaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropServerStatement) {
            return new DropServerStatementContext((DropServerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropStatisticsStatement) {
            return new DropStatisticsStatementContext((DropStatisticsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSubscriptionStatement) {
            return new DropSubscriptionStatementContext((DropSubscriptionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTextSearchStatement) {
            return new DropTextSearchStatementContext((DropTextSearchStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTransformStatement) {
            return new DropTransformStatementContext((DropTransformStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTypeStatement) {
            return new DropTypeStatementContext((DropTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropUserMappingStatement) {
            return new DropUserMappingStatementContext((DropUserMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExecuteStatement) {
            return new ExecuteStatementContext((ExecuteStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof FetchStatement) {
            return new FetchStatementContext((FetchStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ImportForeignSchemaStatement) {
            return new ImportForeignSchemaStatementContext((ImportForeignSchemaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ListenStatement) {
            return new ListenStatementContext((ListenStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MoveStatement) {
            return new MoveStatementContext((MoveStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof NotifyStmtStatement) {
            return new NotifyStmtStatementContext((NotifyStmtStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof OpenStatement) {
            return new OpenStatementContext((OpenStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshMaterializedViewStatement) {
            return new RefreshMaterializedViewStatementContext((RefreshMaterializedViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshMatViewStmtStatement) {
            return new RefreshMatViewStmtStatementContext((RefreshMatViewStmtStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ReindexStatement) {
            return new ReindexStatementContext((ReindexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SecurityLabelStmtStatement) {
            return new SecurityLabelStmtStatementContext((SecurityLabelStmtStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UnlistenStatement) {
            return new UnlistenStatementContext((UnlistenStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMaterializedViewLogStatement) {
            return new CreateMaterializedViewLogStatementContext((CreateMaterializedViewLogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMaterializedViewLogStatement) {
            return new DropMaterializedViewLogStatementContext((DropMaterializedViewLogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterFunctionStatement) {
            return new AlterFunctionStatementContext((AlterFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropFunctionStatement) {
            return new DropFunctionStatementContext((DropFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSequenceStatement) {
            return new CreateSequenceStatementContext((CreateSequenceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSequenceStatement) {
            return new DropSequenceStatementContext((DropSequenceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSynonymStatement) {
            return new AlterSynonymStatementContext((AlterSynonymStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSynonymStatement) {
            return new CreateSynonymStatementContext((CreateSynonymStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePackageStatement) {
            return new CreatePackageStatementContext((CreatePackageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePackageBodyStatement) {
            return new CreatePackageBodyStatementContext((CreatePackageBodyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPackageStatement) {
            return new AlterPackageStatementContext((AlterPackageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterProcedureStatement) {
            return new AlterProcedureStatementContext((AlterProcedureStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTriggerStatement) {
            return new DropTriggerStatementContext((DropTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSessionStatement) {
            return new AlterSessionStatementContext((AlterSessionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMaterializedViewLogStatement) {
            return new AlterMaterializedViewLogStatementContext((AlterMaterializedViewLogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSchemaStatement) {
            return new AlterSchemaStatementContext((AlterSchemaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSequenceStatement) {
            return new AlterSequenceStatementContext((AlterSequenceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterTablespaceStatement) {
            return new AlterTablespaceStatementContext((AlterTablespaceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterApplicationRoleStatement) {
            return new AlterApplicationRoleStatementContext((AlterApplicationRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAssemblyStatement) {
            return new AlterAssemblyStatementContext((AlterAssemblyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAsymmetricKeyStatement) {
            return new AlterAsymmetricKeyStatementContext((AlterAsymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAvailabilityGroupStatement) {
            return new AlterAvailabilityGroupStatementContext((AlterAvailabilityGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterBrokerPriorityStatement) {
            return new AlterBrokerPriorityStatementContext((AlterBrokerPriorityStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterCertificateStatement) {
            return new AlterCertificateStatementContext((AlterCertificateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterColumnEncryptionKeyStatement) {
            return new AlterColumnEncryptionKeyStatementContext((AlterColumnEncryptionKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterCredentialStatement) {
            return new AlterCredentialStatementContext((AlterCredentialStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterCryptographicProviderStatement) {
            return new AlterCryptographicProviderStatementContext((AlterCryptographicProviderStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabaseEncryptionKeyStatement) {
            return new AlterDatabaseEncryptionKeyStatementContext((AlterDatabaseEncryptionKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMasterKeyStatement) {
            return new AlterMasterKeyStatementContext((AlterMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabaseAuditSpecificationStatement) {
            return new AlterDatabaseAuditSpecificationStatementContext((AlterDatabaseAuditSpecificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabaseScopedConfigurationStatement) {
            return new AlterDatabaseScopedConfigurationStatementContext((AlterDatabaseScopedConfigurationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterEndpointStatement) {
            return new AlterEndpointStatementContext((AlterEndpointStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterEventSessionStatement) {
            return new AlterEventSessionStatementContext((AlterEventSessionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterExternalDataSourceStatement) {
            return new AlterExternalDataSourceStatementContext((AlterExternalDataSourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterExternalLanguageStatement) {
            return new AlterExternalLanguageStatementContext((AlterExternalLanguageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterExternalLibraryStatement) {
            return new AlterExternalLibraryStatementContext((AlterExternalLibraryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterExternalResourcePoolStatement) {
            return new AlterExternalResourcePoolStatementContext((AlterExternalResourcePoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterFulltextCatalogStatement) {
            return new AlterFulltextCatalogStatementContext((AlterFulltextCatalogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterFulltextIndexStatement) {
            return new AlterFulltextIndexStatementContext((AlterFulltextIndexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterFulltextStoplistStatement) {
            return new AlterFulltextStoplistStatementContext((AlterFulltextStoplistStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMessageTypeStatement) {
            return new AlterMessageTypeStatementContext((AlterMessageTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPartitionFunctionStatement) {
            return new AlterPartitionFunctionStatementContext((AlterPartitionFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPartitionSchemeStatement) {
            return new AlterPartitionSchemeStatementContext((AlterPartitionSchemeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterQueueStatement) {
            return new AlterQueueStatementContext((AlterQueueStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRemoteServiceBindingStatement) {
            return new AlterRemoteServiceBindingStatementContext((AlterRemoteServiceBindingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterResourceGovernorStatement) {
            return new AlterResourceGovernorStatementContext((AlterResourceGovernorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRouteStatement) {
            return new AlterRouteStatementContext((AlterRouteStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSearchPropertyListStatement) {
            return new AlterSearchPropertyListStatementContext((AlterSearchPropertyListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSecurityPolicyStatement) {
            return new AlterSecurityPolicyStatementContext((AlterSecurityPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServerAuditSpecificationStatement) {
            return new AlterServerAuditSpecificationStatementContext((AlterServerAuditSpecificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServerAuditStatement) {
            return new AlterServerAuditStatementContext((AlterServerAuditStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServerConfigurationStatement) {
            return new AlterServerConfigurationStatementContext((AlterServerConfigurationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServerRoleStatement) {
            return new AlterServerRoleStatementContext((AlterServerRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServiceMasterKeyStatement) {
            return new AlterServiceMasterKeyStatementContext((AlterServiceMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServiceStatement) {
            return new AlterServiceStatementContext((AlterServiceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSymmetricKeyStatement) {
            return new AlterSymmetricKeyStatementContext((AlterSymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterWorkloadGroupStatement) {
            return new AlterWorkloadGroupStatementContext((AlterWorkloadGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterXmlSchemaCollectionStatement) {
            return new AlterXmlSchemaCollectionStatementContext((AlterXmlSchemaCollectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateApplicationRoleStatement) {
            return new CreateApplicationRoleStatementContext((CreateApplicationRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAssemblyStatement) {
            return new CreateAssemblyStatementContext((CreateAssemblyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAsymmetricKeyStatement) {
            return new CreateAsymmetricKeyStatementContext((CreateAsymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAvailabilityGroupStatement) {
            return new CreateAvailabilityGroupStatementContext((CreateAvailabilityGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateBrokerPriorityStatement) {
            return new CreateBrokerPriorityStatementContext((CreateBrokerPriorityStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateCertificateStatement) {
            return new CreateCertificateStatementContext((CreateCertificateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateColumnEncryptionKeyStatement) {
            return new CreateColumnEncryptionKeyStatementContext((CreateColumnEncryptionKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateColumnMasterKeyStatement) {
            return new CreateColumnMasterKeyStatementContext((CreateColumnMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateColumnstoreIndexStatement) {
            return new CreateColumnstoreIndexStatementContext((CreateColumnstoreIndexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateContractStatement) {
            return new CreateContractStatementContext((CreateContractStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateCredentialStatement) {
            return new CreateCredentialStatementContext((CreateCredentialStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateCryptographicProviderStatement) {
            return new CreateCryptographicProviderStatementContext((CreateCryptographicProviderStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDatabaseAuditSpecificationStatement) {
            return new CreateDatabaseAuditSpecificationStatementContext((CreateDatabaseAuditSpecificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDatabaseEncryptionKeyStatement) {
            return new CreateDatabaseEncryptionKeyStatementContext((CreateDatabaseEncryptionKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDatabaseScopedCredentialStatement) {
            return new CreateDatabaseScopedCredentialStatementContext((CreateDatabaseScopedCredentialStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDefaultStatement) {
            return new CreateDefaultStatementContext((CreateDefaultStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEndpointStatement) {
            return new CreateEndpointStatementContext((CreateEndpointStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEventNotificationStatement) {
            return new CreateEventNotificationStatementContext((CreateEventNotificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEventSessionStatement) {
            return new CreateEventSessionStatementContext((CreateEventSessionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExternalDataSourceStatement) {
            return new CreateExternalDataSourceStatementContext((CreateExternalDataSourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExternalFileFormatStatement) {
            return new CreateExternalFileFormatStatementContext((CreateExternalFileFormatStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExternalLanguageStatement) {
            return new CreateExternalLanguageStatementContext((CreateExternalLanguageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExternalLibraryStatement) {
            return new CreateExternalLibraryStatementContext((CreateExternalLibraryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExternalResourcePoolStatement) {
            return new CreateExternalResourcePoolStatementContext((CreateExternalResourcePoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateExternalTableStatement) {
            return new CreateExternalTableStatementContext((CreateExternalTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateFulltextCatalogStatement) {
            return new CreateFulltextCatalogStatementContext((CreateFulltextCatalogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateFulltextIndexStatement) {
            return new CreateFulltextIndexStatementContext((CreateFulltextIndexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateFulltextStoplistStatement) {
            return new CreateFulltextStoplistStatementContext((CreateFulltextStoplistStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMasterKeyStatement) {
            return new CreateMasterKeyStatementContext((CreateMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMessageTypeStatement) {
            return new CreateMessageTypeStatementContext((CreateMessageTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePartitionFunctionStatement) {
            return new CreatePartitionFunctionStatementContext((CreatePartitionFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePartitionSchemeStatement) {
            return new CreatePartitionSchemeStatementContext((CreatePartitionSchemeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateQueueStatement) {
            return new CreateQueueStatementContext((CreateQueueStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRemoteServiceBindingStatement) {
            return new CreateRemoteServiceBindingStatementContext((CreateRemoteServiceBindingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRouteStatement) {
            return new CreateRouteStatementContext((CreateRouteStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSearchPropertyListStatement) {
            return new CreateSearchPropertyListStatementContext((CreateSearchPropertyListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSecurityPolicyStatement) {
            return new CreateSecurityPolicyStatementContext((CreateSecurityPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateServerAuditSpecificationStatement) {
            return new CreateServerAuditSpecificationStatementContext((CreateServerAuditSpecificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateServerAuditStatement) {
            return new CreateServerAuditStatementContext((CreateServerAuditStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateServerRoleStatement) {
            return new CreateServerRoleStatementContext((CreateServerRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSymmetricKeyStatement) {
            return new CreateSymmetricKeyStatementContext((CreateSymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateWorkloadGroupStatement) {
            return new CreateWorkloadGroupStatementContext((CreateWorkloadGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateXmlSchemaCollectionStatement) {
            return new CreateXmlSchemaCollectionStatementContext((CreateXmlSchemaCollectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropBrokerPriorityStatement) {
            return new DropBrokerPriorityStatementContext((DropBrokerPriorityStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropCertificateStatement) {
            return new DropCertificateStatementContext((DropCertificateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropContractStatement) {
            return new DropContractStatementContext((DropContractStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropEventNotificationStatement) {
            return new DropEventNotificationStatementContext((DropEventNotificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropEventSessionStatement) {
            return new DropEventSessionStatementContext((DropEventSessionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropExternalFileFormatStatement) {
            return new DropExternalFileFormatStatementContext((DropExternalFileFormatStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropExternalLanguageStatement) {
            return new DropExternalLanguageStatementContext((DropExternalLanguageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropExternalLibraryStatement) {
            return new DropExternalLibraryStatementContext((DropExternalLibraryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropExternalResourcePoolStatement) {
            return new DropExternalResourcePoolStatementContext((DropExternalResourcePoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropExternalTableStatement) {
            return new DropExternalTableStatementContext((DropExternalTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropFulltextCatalogStatement) {
            return new DropFulltextCatalogStatementContext((DropFulltextCatalogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropFulltextIndexStatement) {
            return new DropFulltextIndexStatementContext((DropFulltextIndexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropFulltextStoplistStatement) {
            return new DropFulltextStoplistStatementContext((DropFulltextStoplistStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMasterKeyStatement) {
            return new DropMasterKeyStatementContext((DropMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMessageTypeStatement) {
            return new DropMessageTypeStatementContext((DropMessageTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPartitionFunctionStatement) {
            return new DropPartitionFunctionStatementContext((DropPartitionFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPartitionSchemeStatement) {
            return new DropPartitionSchemeStatementContext((DropPartitionSchemeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropQueueStatement) {
            return new DropQueueStatementContext((DropQueueStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRemoteServiceBindingStatement) {
            return new DropRemoteServiceBindingStatementContext((DropRemoteServiceBindingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRouteStatement) {
            return new DropRouteStatementContext((DropRouteStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSearchPropertyListStatement) {
            return new DropSearchPropertyListStatementContext((DropSearchPropertyListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSecurityPolicyStatement) {
            return new DropSecurityPolicyStatementContext((DropSecurityPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropServerAuditSpecificationStatement) {
            return new DropServerAuditSpecificationStatementContext((DropServerAuditSpecificationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropServerAuditStatement) {
            return new DropServerAuditStatementContext((DropServerAuditStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropServerRoleStatement) {
            return new DropServerRoleStatementContext((DropServerRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSymmetricKeyStatement) {
            return new DropSymmetricKeyStatementContext((DropSymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropWorkloadGroupStatement) {
            return new DropWorkloadGroupStatementContext((DropWorkloadGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropXmlSchemaCollectionStatement) {
            return new DropXmlSchemaCollectionStatementContext((DropXmlSchemaCollectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof EnableTriggerStatement) {
            return new EnableTriggerStatementContext((EnableTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AnalyzeStatement) {
            return new AnalyzeStatementContext((AnalyzeStatement) sqlStatement);
        }
        if (sqlStatement instanceof CreateTriggerStatement) {
            return new CreateTriggerStatementContext((CreateTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CursorStatement) {
            return new CursorStatementContext((CursorStatement) sqlStatement);
        }
        if (sqlStatement instanceof DropPackageStatement) {
            return new DropPackageStatementContext((DropPackageStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSynonymStatement) {
            return new DropSynonymStatementContext((DropSynonymStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DisableTriggerStatement) {
            return new DisableTriggerStatementContext((DisableTriggerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDataConnectorStatement) {
            return new AlterDataConnectorStatementContext((AlterDataConnectorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDataConnectorStatement) {
            return new CreateDataConnectorStatementContext((CreateDataConnectorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDataConnectorStatement) {
            return new DropDataConnectorStatementContext((DropDataConnectorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMacroStatement) {
            return new CreateMacroStatementContext((CreateMacroStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMacroStatement) {
            return new DropMacroStatementContext((DropMacroStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabaseStatement) {
            return new AlterDatabaseStatementContext((AlterDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateContextStatement) {
            return new CreateContextStatementContext((CreateContextStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropContextStatement) {
            return new DropContextStatementContext((DropContextStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRollbackSegmentStatement) {
            return new DropRollbackSegmentStatementContext((DropRollbackSegmentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof StatTableStatement) {
            return new StatTableStatementContext((StatTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof PLSQLBlockStatement) {
            return new PLSQLBlockStatementContext((PLSQLBlockStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof FlashbackTableStatement) {
            return new FlashbackTableStatementContext((FlashbackTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof PurgeStatement) {
            return new PurgeStatementContext((PurgeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof NoAuditStatement) {
            return new NoAuditStatementContext((NoAuditStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SwitchStatement) {
            return new SwitchStatementContext((SwitchStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SystemActionStatement) {
            return new SystemActionStatementContext((SystemActionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetTimeZoneStatement) {
            return new SetTimeZoneStatementContext((SetTimeZoneStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAnalyticViewStatement) {
            return new AlterAnalyticViewStatementContext((AlterAnalyticViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAttributeDimensionStatement) {
            return new AlterAttributeDimensionStatementContext((AlterAttributeDimensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAuditPolicyStatement) {
            return new AlterAuditPolicyStatementContext((AlterAuditPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterClusterStatement) {
            return new AlterClusterStatementContext((AlterClusterStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDimensionStatement) {
            return new AlterDimensionStatementContext((AlterDimensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDiskgroupStatement) {
            return new AlterDiskgroupStatementContext((AlterDiskgroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterFlashbackArchiveStatement) {
            return new AlterFlashbackArchiveStatementContext((AlterFlashbackArchiveStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterHierarchyStatement) {
            return new AlterHierarchyStatementContext((AlterHierarchyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterInMemoryJoinGroupStatement) {
            return new AlterInMemoryJoinGroupStatementContext((AlterInMemoryJoinGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterIndexTypeStatement) {
            return new AlterIndexTypeStatementContext((AlterIndexTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterJavaStatement) {
            return new AlterJavaStatementContext((AlterJavaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterLibraryStatement) {
            return new AlterLibraryStatementContext((AlterLibraryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterLockdownProfileStatement) {
            return new AlterLockdownProfileStatementContext((AlterLockdownProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMaterializedZoneMapStatement) {
            return new AlterMaterializedZoneMapStatementContext((AlterMaterializedZoneMapStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterOutlineStatement) {
            return new AlterOutlineStatementContext((AlterOutlineStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPluggableDatabaseStatement) {
            return new AlterPluggableDatabaseStatementContext((AlterPluggableDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterProfileStatement) {
            return new AlterProfileStatementContext((AlterProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRollbackSegmentStatement) {
            return new AlterRollbackSegmentStatementContext((AlterRollbackSegmentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRollbackSegmentStatement) {
            return new CreateRollbackSegmentStatementContext((CreateRollbackSegmentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterBufferPoolStatement) {
            return new AlterBufferPoolStatementContext((AlterBufferPoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabasePartitionGroupStatement) {
            return new AlterDatabasePartitionGroupStatementContext((AlterDatabasePartitionGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterEventMonitorStatement) {
            return new AlterEventMonitorStatementContext((AlterEventMonitorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterHistogramTemplateStatement) {
            return new AlterHistogramTemplateStatementContext((AlterHistogramTemplateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMaskStatement) {
            return new AlterMaskStatementContext((AlterMaskStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterMethodStatement) {
            return new AlterMethodStatementContext((AlterMethodStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterModuleStatement) {
            return new AlterModuleStatementContext((AlterModuleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterNicknameStatement) {
            return new AlterNicknameStatementContext((AlterNicknameStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPermissionStatement) {
            return new AlterPermissionStatementContext((AlterPermissionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterProcedureExternalStatement) {
            return new AlterProcedureExternalStatementContext((AlterProcedureExternalStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSecurityLabelComponentStatement) {
            return new AlterSecurityLabelComponentStatementContext((AlterSecurityLabelComponentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterServiceClassStatement) {
            return new AlterServiceClassStatementContext((AlterServiceClassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterStogroupStatement) {
            return new AlterStogroupStatementContext((AlterStogroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterThresholdStatement) {
            return new AlterThresholdStatementContext((AlterThresholdStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterTrustedContextStatement) {
            return new AlterTrustedContextStatementContext((AlterTrustedContextStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterUsageListStatement) {
            return new AlterUsageListStatementContext((AlterUsageListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterWorkActionSetStatement) {
            return new AlterWorkActionSetStatementContext((AlterWorkActionSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterWorkClassSetStatement) {
            return new AlterWorkClassSetStatementContext((AlterWorkClassSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterWorkloadStatement) {
            return new AlterWorkloadStatementContext((AlterWorkloadStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterWrapperStatement) {
            return new AlterWrapperStatementContext((AlterWrapperStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterXsrObjectStatement) {
            return new AlterXsrObjectStatementContext((AlterXsrObjectStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAliasStatement) {
            return new CreateAliasStatementContext((CreateAliasStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateAuditPolicyStatement) {
            return new CreateAuditPolicyStatementContext((CreateAuditPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateBufferPoolStatement) {
            return new CreateBufferPoolStatementContext((CreateBufferPoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDatabasePartitionGroupStatement) {
            return new CreateDatabasePartitionGroupStatementContext((CreateDatabasePartitionGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEventMonitorStatement) {
            return new CreateEventMonitorStatementContext((CreateEventMonitorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateFunctionMappingStatement) {
            return new CreateFunctionMappingStatementContext((CreateFunctionMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateGlobalTemporaryTableStatement) {
            return new CreateGlobalTemporaryTableStatementContext((CreateGlobalTemporaryTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateHistogramTemplateStatement) {
            return new CreateHistogramTemplateStatementContext((CreateHistogramTemplateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateIndexExtensionStatement) {
            return new CreateIndexExtensionStatementContext((CreateIndexExtensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMaskStatement) {
            return new CreateMaskStatementContext((CreateMaskStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateMethodStatement) {
            return new CreateMethodStatementContext((CreateMethodStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateModuleStatement) {
            return new CreateModuleStatementContext((CreateModuleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateNicknameStatement) {
            return new CreateNicknameStatementContext((CreateNicknameStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePermissionStatement) {
            return new CreatePermissionStatementContext((CreatePermissionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSecurityLabelComponentStatement) {
            return new CreateSecurityLabelComponentStatementContext((CreateSecurityLabelComponentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSecurityLabelStatement) {
            return new CreateSecurityLabelStatementContext((CreateSecurityLabelStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateServerStatement) {
            return new CreateServerStatementContext((CreateServerStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateServiceClassStatement) {
            return new CreateServiceClassStatementContext((CreateServiceClassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateStogroupStatement) {
            return new CreateStogroupStatementContext((CreateStogroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTenantStatement) {
            return new CreateTenantStatementContext((CreateTenantStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateThresholdStatement) {
            return new CreateThresholdStatementContext((CreateThresholdStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTrustedContextStatement) {
            return new CreateTrustedContextStatementContext((CreateTrustedContextStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTypeMappingStatement) {
            return new CreateTypeMappingStatementContext((CreateTypeMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateUsageListStatement) {
            return new CreateUsageListStatementContext((CreateUsageListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateVariableStatement) {
            return new CreateVariableStatementContext((CreateVariableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateWorkActionSetStatement) {
            return new CreateWorkActionSetStatementContext((CreateWorkActionSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateWorkClassSetStatement) {
            return new CreateWorkClassSetStatementContext((CreateWorkClassSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateWorkloadStatement) {
            return new CreateWorkloadStatementContext((CreateWorkloadStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateWrapperStatement) {
            return new CreateWrapperStatementContext((CreateWrapperStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropAliasStatement) {
            return new DropAliasStatementContext((DropAliasStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropAuditPolicyStatement) {
            return new DropAuditPolicyStatementContext((DropAuditPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropBufferPoolStatement) {
            return new DropBufferPoolStatementContext((DropBufferPoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDatabasePartitionGroupStatement) {
            return new DropDatabasePartitionGroupStatementContext((DropDatabasePartitionGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDataLakeTableStatement) {
            return new DropDataLakeTableStatementContext((DropDataLakeTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropEventMonitorStatement) {
            return new DropEventMonitorStatementContext((DropEventMonitorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropFunctionMappingStatement) {
            return new DropFunctionMappingStatementContext((DropFunctionMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropHistogramTemplateStatement) {
            return new DropHistogramTemplateStatementContext((DropHistogramTemplateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropIndexExtensionStatement) {
            return new DropIndexExtensionStatementContext((DropIndexExtensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMaskStatement) {
            return new DropMaskStatementContext((DropMaskStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMethodStatement) {
            return new DropMethodStatementContext((DropMethodStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropModelStatement) {
            return new DropModelStatementContext((DropModelStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropModuleStatement) {
            return new DropModuleStatementContext((DropModuleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropNicknameStatement) {
            return new DropNicknameStatementContext((DropNicknameStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPermissionStatement) {
            return new DropPermissionStatementContext((DropPermissionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSecurityLabelComponentStatement) {
            return new DropSecurityLabelComponentStatementContext((DropSecurityLabelComponentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSecurityLabelStatement) {
            return new DropSecurityLabelStatementContext((DropSecurityLabelStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropServiceClassStatement) {
            return new DropServiceClassStatementContext((DropServiceClassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropStogroupStatement) {
            return new DropStogroupStatementContext((DropStogroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTablespaceStatement) {
            return new DropTablespaceStatementContext((DropTablespaceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTenantStatement) {
            return new DropTenantStatementContext((DropTenantStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropThresholdStatement) {
            return new DropThresholdStatementContext((DropThresholdStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTrustedContextStatement) {
            return new DropTrustedContextStatementContext((DropTrustedContextStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropTypeMappingStatement) {
            return new DropTypeMappingStatementContext((DropTypeMappingStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropUsageListStatement) {
            return new DropUsageListStatementContext((DropUsageListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropVariableStatement) {
            return new DropVariableStatementContext((DropVariableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropWorkActionSetStatement) {
            return new DropWorkActionSetStatementContext((DropWorkActionSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropWorkClassSetStatement) {
            return new DropWorkClassSetStatementContext((DropWorkClassSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropWorkloadStatement) {
            return new DropWorkloadStatementContext((DropWorkloadStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropWrapperStatement) {
            return new DropWrapperStatementContext((DropWrapperStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropXsrObjectStatement) {
            return new DropXsrObjectStatementContext((DropXsrObjectStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DeclareVariableStatement) {
            return new DeclareVariableStatementContext((DeclareVariableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshFunctionStatement) {
            return new RefreshFunctionStatementContext((RefreshFunctionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshStatement) {
            return new RefreshStatementContext((RefreshStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshTableStatement) {
            return new RefreshTableStatementContext((RefreshTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ReplaceTableStatement) {
            return new ReplaceTableStatementContext((ReplaceTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ComputeStatsStatement) {
            return new ComputeStatsStatementContext((ComputeStatsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof InvalidateMetadataStatement) {
            return new InvalidateMetadataStatementContext((InvalidateMetadataStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshFunctionsStatement) {
            return new RefreshFunctionsStatementContext((RefreshFunctionsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DescribeStatement) {
            return new DescribeStatementContext((DescribeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterNamedCollectionStatement) {
            return new AlterNamedCollectionStatementContext((AlterNamedCollectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterQuotaStatement) {
            return new AlterQuotaStatementContext((AlterQuotaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterSettingsProfileStatement) {
            return new AlterSettingsProfileStatementContext((AlterSettingsProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AttachDatabaseStatement) {
            return new AttachDatabaseStatementContext((AttachDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AttachDictionaryStatement) {
            return new AttachDictionaryStatementContext((AttachDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AttachTableStatement) {
            return new AttachTableStatementContext((AttachTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDictionaryStatement) {
            return new CreateDictionaryStatementContext((CreateDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateNamedCollectionStatement) {
            return new CreateNamedCollectionStatementContext((CreateNamedCollectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSettingsProfileStatement) {
            return new CreateSettingsProfileStatementContext((CreateSettingsProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateWindowViewStatement) {
            return new CreateWindowViewStatementContext((CreateWindowViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DetachDatabaseStatement) {
            return new DetachDatabaseStatementContext((DetachDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DetachDictionaryStatement) {
            return new DetachDictionaryStatementContext((DetachDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DetachTableStatement) {
            return new DetachTableStatementContext((DetachTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DetachViewStatement) {
            return new DetachViewStatementContext((DetachViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDictionaryStatement) {
            return new DropDictionaryStatementContext((DropDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropNamedCollectionStatement) {
            return new DropNamedCollectionStatementContext((DropNamedCollectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropProfileStatement) {
            return new DropProfileStatementContext((DropProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropQuotaStatement) {
            return new DropQuotaStatementContext((DropQuotaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExchangeDictionaryStatement) {
            return new ExchangeDictionaryStatementContext((ExchangeDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExchangeTableStatement) {
            return new ExchangeTableStatementContext((ExchangeTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MoveQuotaStatement) {
            return new MoveQuotaStatementContext((MoveQuotaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MoveRoleStatement) {
            return new MoveRoleStatementContext((MoveRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MoveRowPolicyStatement) {
            return new MoveRowPolicyStatementContext((MoveRowPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MoveSettingsStatement) {
            return new MoveSettingsStatementContext((MoveSettingsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof MoveUserStatement) {
            return new MoveUserStatementContext((MoveUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameDatabaseStatement) {
            return new RenameDatabaseStatementContext((RenameDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameDictionaryStatement) {
            return new RenameDictionaryStatementContext((RenameDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof WatchLiveViewStatement) {
            return new WatchLiveViewStatementContext((WatchLiveViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterScheduleStatement) {
            return new AlterScheduleStatementContext((AlterScheduleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AnnotateStatement) {
            return new AnnotateStatementContext((AnnotateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateGraphWorkspaceStatement) {
            return new CreateGraphWorkspaceStatementContext((CreateGraphWorkspaceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateProjectionViewStatement) {
            return new CreateProjectionViewStatementContext((CreateProjectionViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSchedulerJobStatement) {
            return new CreateSchedulerJobStatementContext((CreateSchedulerJobStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropCredentialStatement) {
            return new DropCredentialStatementContext((DropCredentialStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropGraphWorkspaceStatement) {
            return new DropGraphWorkspaceStatementContext((DropGraphWorkspaceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSchedulerJobStatement) {
            return new DropSchedulerJobStatementContext((DropSchedulerJobStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RefreshStatisticsStatement) {
            return new RefreshStatisticsStatementContext((RefreshStatisticsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameColumnStatement) {
            return new RenameColumnStatementContext((RenameColumnStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameIndexStatement) {
            return new RenameIndexStatementContext((RenameIndexStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameSchemaStatement) {
            return new RenameSchemaStatementContext((RenameSchemaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabaseDictionaryStatement) {
            return new AlterDatabaseDictionaryStatementContext((AlterDatabaseDictionaryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterDatabaseLinkStatement) {
            return new AlterDatabaseLinkStatementContext((AlterDatabaseLinkStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateClassBodyStatement) {
            return new CreateClassBodyStatementContext((CreateClassBodyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateClassStatement) {
            return new CreateClassStatementContext((CreateClassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateInMemoryJoinGroupStatement) {
            return new CreateInMemoryJoinGroupStatementContext((CreateInMemoryJoinGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateControlFileStatement) {
            return new CreateControlFileStatementContext((CreateControlFileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDimensionStatement) {
            return new CreateDimensionStatementContext((CreateDimensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDirectoryStatement) {
            return new CreateDirectoryStatementContext((CreateDirectoryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDiskgroupStatement) {
            return new CreateDiskgroupStatementContext((CreateDiskgroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEditionStatement) {
            return new CreateEditionStatementContext((CreateEditionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateFlashbackArchiveStatement) {
            return new CreateFlashbackArchiveStatementContext((CreateFlashbackArchiveStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateJavaStatement) {
            return new CreateJavaStatementContext((CreateJavaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateLibraryStatement) {
            return new CreateLibraryStatementContext((CreateLibraryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateLockdownProfileStatement) {
            return new CreateLockdownProfileStatementContext((CreateLockdownProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateNestedTableTypeStatement) {
            return new CreateNestedTableTypeStatementContext((CreateNestedTableTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateObjectTypeStatement) {
            return new CreateObjectTypeStatementContext((CreateObjectTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePFileStatement) {
            return new CreatePFileStatementContext((CreatePFileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateProfileStatement) {
            return new CreateProfileStatementContext((CreateProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRestorePointStatement) {
            return new CreateRestorePointStatementContext((CreateRestorePointStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateSPFileStatement) {
            return new CreateSPFileStatementContext((CreateSPFileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateVarrayTypeStatement) {
            return new CreateVarrayTypeStatementContext((CreateVarrayTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DisassociateStatisticsStatement) {
            return new DisassociateStatisticsStatementContext((DisassociateStatisticsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropClassStatement) {
            return new DropClassStatementContext((DropClassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropClusterStatement) {
            return new DropClusterStatementContext((DropClusterStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDimensionStatement) {
            return new DropDimensionStatementContext((DropDimensionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDirectoryStatement) {
            return new DropDirectoryStatementContext((DropDirectoryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropDiskgroupStatement) {
            return new DropDiskgroupStatementContext((DropDiskgroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropEditionStatement) {
            return new DropEditionStatementContext((DropEditionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropFlashbackArchiveStatement) {
            return new DropFlashbackArchiveStatementContext((DropFlashbackArchiveStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropIndexTypeStatement) {
            return new DropIndexTypeStatementContext((DropIndexTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropInMemoryJoinGroupStatement) {
            return new DropInMemoryJoinGroupStatementContext((DropInMemoryJoinGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropJavaStatement) {
            return new DropJavaStatementContext((DropJavaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropLibraryStatement) {
            return new DropLibraryStatementContext((DropLibraryStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropLockdownProfileStatement) {
            return new DropLockdownProfileStatementContext((DropLockdownProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropMaterializedZoneMapStatement) {
            return new DropMaterializedZoneMapStatementContext((DropMaterializedZoneMapStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropOutlineStatement) {
            return new DropOutlineStatementContext((DropOutlineStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPluggableDatabaseStatement) {
            return new DropPluggableDatabaseStatementContext((DropPluggableDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRestorePointStatement) {
            return new DropRestorePointStatementContext((DropRestorePointStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterAccessMethodStatement) {
            return new AlterAccessMethodStatementContext((AlterAccessMethodStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterFragmentStatement) {
            return new AlterFragmentStatementContext((AlterFragmentStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateDistinctTypeStatement) {
            return new CreateDistinctTypeStatementContext((CreateDistinctTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateOpaqueTypeStatement) {
            return new CreateOpaqueTypeStatementContext((CreateOpaqueTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateOpclassStatement) {
            return new CreateOpclassStatementContext((CreateOpclassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRoutineFromStatement) {
            return new CreateRoutineFromStatementContext((CreateRoutineFromStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRowTypeStatement) {
            return new CreateRowTypeStatementContext((CreateRowTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateTempTableStatement) {
            return new CreateTempTableStatementContext((CreateTempTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateXadatasourceStatement) {
            return new CreateXadatasourceStatementContext((CreateXadatasourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateXadatasourceTypeStatement) {
            return new CreateXadatasourceTypeStatementContext((CreateXadatasourceTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropOpclassStatement) {
            return new DropOpclassStatementContext((DropOpclassStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRowTypeStatement) {
            return new DropRowTypeStatementContext((DropRowTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropSecurityStatement) {
            return new DropSecurityStatementContext((DropSecurityStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropXadatasourceStatement) {
            return new DropXadatasourceStatementContext((DropXadatasourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropXadataTypeSourceStatement) {
            return new DropXadataTypeSourceStatementContext((DropXadataTypeSourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameSecurityStatement) {
            return new RenameSecurityStatementContext((RenameSecurityStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameTrustedStatement) {
            return new RenameTrustedStatementContext((RenameTrustedStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateEventStatement) {
            return new CreateEventStatementContext((CreateEventStatement) sqlStatement, currentDatabaseName);
        }
        return new UnknownSQLStatementContext(sqlStatement);
    }

    private static SQLStatementContext getDCLStatementContext(final DCLStatement sqlStatement, final String currentDatabaseName) {
        if (sqlStatement instanceof GrantStatement) {
            return new GrantStatementContext((GrantStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RevokeStatement) {
            return new RevokeStatementContext((RevokeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DenyUserStatement) {
            return new DenyUserStatementContext((DenyUserStatement) sqlStatement);
        }
        if (sqlStatement instanceof AlterAuthorizationStatement) {
            return new AlterAuthorizationStatementContext((AlterAuthorizationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ControlOfFlowStatement) {
            return new ControlOfFlowStatementContext((ControlOfFlowStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExecuteAsStatement) {
            return new ExecuteAsStatementContext((ExecuteAsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof OpenMasterKeyStatement) {
            return new OpenMasterKeyStatementContext((OpenMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof OpenSymmetricKeyStatement) {
            return new OpenSymmetricKeyStatementContext((OpenSymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CloseMasterKeyStatement) {
            return new CloseMasterKeyStatementContext((CloseMasterKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CloseSymmetricKeyStatement) {
            return new CloseSymmetricKeyStatementContext((CloseSymmetricKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRoleStatement) {
            return new CreateRoleStatementContext((CreateRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRoleStatement) {
            return new DropRoleStatementContext((DropRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetRoleStatement) {
            return new SetRoleStatementContext((SetRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRoleStatement) {
            return new AlterRoleStatementContext((AlterRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterUserStatement) {
            return new AlterUserStatementContext((AlterUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateUserStatement) {
            return new CreateUserStatementContext((CreateUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropUserStatement) {
            return new DropUserStatementContext((DropUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetDefaultRoleStatement) {
            return new SetDefaultRoleStatementContext((SetDefaultRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterProviderStatement) {
            return new AlterProviderStatementContext((AlterProviderStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterRemoteSourceStatement) {
            return new AlterRemoteSourceStatementContext((AlterRemoteSourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterStructuredPrivilegeStatement) {
            return new AlterStructuredPrivilegeStatementContext((AlterStructuredPrivilegeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterUserGroupStatement) {
            return new AlterUserGroupStatementContext((AlterUserGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateProviderStatement) {
            return new CreateProviderStatementContext((CreateProviderStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateRemoteSourceStatement) {
            return new CreateRemoteSourceStatementContext((CreateRemoteSourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateStructuredPrivilegeStatement) {
            return new CreateStructuredPrivilegeStatementContext((CreateStructuredPrivilegeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateUserGroupStatement) {
            return new CreateUserGroupStatementContext((CreateUserGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropProviderStatement) {
            return new DropProviderStatementContext((DropProviderStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropRemoteSourceStatement) {
            return new DropRemoteSourceStatementContext((DropRemoteSourceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropStructuredPrivilegeStatement) {
            return new DropStructuredPrivilegeStatementContext((DropStructuredPrivilegeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropUserGroupStatement) {
            return new DropUserGroupStatementContext((DropUserGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ValidateProviderStatement) {
            return new ValidateProviderStatementContext((ValidateProviderStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ValidateUserStatement) {
            return new ValidateUserStatementContext((ValidateUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RenameUserStatement) {
            return new RenameUserStatementContext((RenameUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetPasswordStatement) {
            return new SetPasswordStatementContext((SetPasswordStatement) sqlStatement, currentDatabaseName);
        }
        return new UnknownSQLStatementContext(sqlStatement);
    }

    private static SQLStatementContext getTCLStatementContext(final TCLStatement sqlStatement, final String currentDatabaseName) {
        if (sqlStatement instanceof BeginTransactionStatement) {
            return new BeginTransactionStatementContext((BeginTransactionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CommitPreparedStatement) {
            return new CommitPreparedStatementContext((CommitPreparedStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CommitStatement) {
            return new CommitStatementContext((CommitStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof LockStatement) {
            return new LockStatementContext((LockStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof PrepareTransactionStatement) {
            return new PrepareTransactionStatementContext((PrepareTransactionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ReleaseSavepointStatement) {
            return new ReleaseSavepointStatementContext((ReleaseSavepointStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RollbackPreparedStatement) {
            return new RollbackPreparedStatementContext((RollbackPreparedStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RollbackStatement) {
            return new RollbackStatementContext((RollbackStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SavepointStatement) {
            return new SavepointStatementContext((SavepointStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetConstraintsStatement) {
            return new SetConstraintsStatementContext((SetConstraintsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetTransactionStatement) {
            return new SetTransactionStatementContext((SetTransactionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof StartTransactionStatement) {
            return new StartTransactionStatementContext((StartTransactionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetAutoCommitStatement) {
            return new SetAutoCommitStatementContext((SetAutoCommitStatement) sqlStatement, currentDatabaseName);
        }
        return new UnknownSQLStatementContext(sqlStatement);
    }

    private static SQLStatementContext getDALStatementContext(final DALStatement sqlStatement, final String currentDatabaseName) {
        if (sqlStatement instanceof ExplainStatement) {
            return new ExplainStatementContext((ExplainStatement) sqlStatement);
        }
        if (sqlStatement instanceof ShowColumnsStatement) {
            return new ShowColumnsStatementContext((ShowColumnsStatement) sqlStatement);
        }
        if (sqlStatement instanceof ShowTablesStatement) {
            return new ShowTablesStatementContext((ShowTablesStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowTableStatusStatement) {
            return new ShowTableStatusStatementContext((ShowTableStatusStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowIndexStatement) {
            return new ShowIndexStatementContext((ShowIndexStatement) sqlStatement);
        }
        if (sqlStatement instanceof ShowStatement) {
            return new ShowStatementContext((ShowStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AnalyzeTableStatement) {
            return new AnalyzeTableStatementContext((AnalyzeTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof FlushStatement) {
            return new FlushStatementContext((FlushStatement) sqlStatement);
        }
        if (sqlStatement instanceof SetStatement) {
            return new SetStatementContext((SetStatement) sqlStatement);
        }
        if (sqlStatement instanceof ChecksumTableStatement) {
            return new ChecksumTableStatementContext((ChecksumTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CheckTableStatement) {
            return new CheckTableStatementContext((CheckTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateResourceGroupStatement) {
            return new CreateResourceGroupStatementContext((CreateResourceGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropResourceGroupStatement) {
            return new DropResourceGroupStatementContext((DropResourceGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof OptimizeTableStatement) {
            return new OptimizeTableStatementContext((OptimizeTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof RepairTableStatement) {
            return new RepairTableStatementContext((RepairTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof ResetParameterStatement) {
            return new ResetParameterStatementContext((ResetParameterStatement) sqlStatement);
        }
        if (sqlStatement instanceof ShowCreateTableStatement) {
            return new ShowCreateTableStatementContext((ShowCreateTableStatement) sqlStatement);
        }
        if (sqlStatement instanceof ShowCreateViewStatement) {
            return new ShowCreateViewStatementContext((ShowCreateViewStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowFilesStatement) {
            return new ShowFilesStatementContext(((ShowFilesStatement) sqlStatement), currentDatabaseName);
        }
        if (sqlStatement instanceof ShowGrantStatement) {
            return new ShowGrantStatementContext(((ShowGrantStatement) sqlStatement), currentDatabaseName);
        }
        if (sqlStatement instanceof ShowRolesStatement) {
            return new ShowRolesStatementContext(((ShowRolesStatement) sqlStatement), currentDatabaseName);
        }
        if (sqlStatement instanceof ShowStatsStatement) {
            return new ShowStatsStatementContext(((ShowStatsStatement) sqlStatement), currentDatabaseName);
        }
        if (sqlStatement instanceof ShutdownStatement) {
            return new ShutdownStatementContext(((ShutdownStatement) sqlStatement), currentDatabaseName);
        }
        if (sqlStatement instanceof UseStatement) {
            return new UseStatementContext((UseStatement) sqlStatement);
        }
        if (sqlStatement instanceof BackupStatement) {
            return new BackupStatementContext((BackupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RestoreStatement) {
            return new RestoreStatementContext((RestoreStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ConversationStatement) {
            return new ConversationStatementContext((ConversationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ReconfigureStatement) {
            return new ReconfigureStatementContext((ReconfigureStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof VacuumStatement) {
            return new VacuumStatementContext((VacuumStatement) sqlStatement);
        }
        if (sqlStatement instanceof AlterResourceCostStatement) {
            return new AlterResourceCostStatementContext((AlterResourceCostStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SpoolStatement) {
            return new SpoolStatementContext((SpoolStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetResourceGroupStatement) {
            return new SetResourceGroupStatementContext((SetResourceGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof BinlogStatement) {
            return new BinlogStatementContext((BinlogStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowStatusStatement) {
            return new ShowStatusStatementContext((ShowStatusStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DatabaseStatement) {
            return new DatabaseStatementContext((DatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AllocateStatement) {
            return new AllocateStatementContext((AllocateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AssociateLocatorsStatement) {
            return new AssociateLocatorsStatementContext((AssociateLocatorsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof BeginDeclareSectionStatement) {
            return new BeginDeclareSectionStatementContext((BeginDeclareSectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ConnectTypeStatement) {
            return new ConnectTypeStatementContext((ConnectTypeStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DeclareCursorStatement) {
            return new DeclareCursorStatementContext((DeclareCursorStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DeclareGlobalTemporaryTableStatement) {
            return new DeclareGlobalTemporaryTableStatementContext((DeclareGlobalTemporaryTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DisconnectStatement) {
            return new DisconnectStatementContext((DisconnectStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof EndDeclareSectionStatement) {
            return new EndDeclareSectionStatementContext((EndDeclareSectionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AddFileStatement) {
            return new AddFileStatementContext((AddFileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AddJarStatement) {
            return new AddJarStatementContext((AddJarStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CacheTableStatement) {
            return new CacheTableStatementContext((CacheTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ClearCacheStatement) {
            return new ClearCacheStatementContext((ClearCacheStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExecuteImmediateStatement) {
            return new ExecuteImmediateStatementContext((ExecuteImmediateStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ListStatement) {
            return new ListStatementContext((ListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCatalogsStatement) {
            return new ShowCatalogsStatementContext((ShowCatalogsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCurrentNamespaceStatement) {
            return new ShowCurrentNamespaceStatementContext((ShowCurrentNamespaceStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowDatabasesStatement) {
            return new ShowDatabasesStatementContext((ShowDatabasesStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowFunctionsStatement) {
            return new ShowFunctionsStatementContext((ShowFunctionsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowPartitionsStatement) {
            return new ShowPartitionsStatementContext((ShowPartitionsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowProceduresStatement) {
            return new ShowProceduresStatementContext((ShowProceduresStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowTableExtendedStatement) {
            return new ShowTableExtendedStatementContext((ShowTableExtendedStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowTblPropertiesStatement) {
            return new ShowTblPropertiesStatementContext((ShowTblPropertiesStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UncacheTableStatement) {
            return new UncacheTableStatementContext((UncacheTableStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UnsupportedHiveNativeCommandsStatement) {
            return new UnsupportedHiveNativeCommandsStatementContext((UnsupportedHiveNativeCommandsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CheckGrantStatement) {
            return new CheckGrantStatementContext((CheckGrantStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExistsStatement) {
            return new ExistsStatementContext((ExistsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof KillStatement) {
            return new KillStatementContext((KillStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof  ShowCreateDatabaseStatement) {
            return new ShowCreateDatabaseStatementContext((ShowCreateDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCreateProfileStatement) {
            return new ShowCreateProfileStatementContext((ShowCreateProfileStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCreateQuotaStatement) {
            return new ShowCreateQuotaStatementContext((ShowCreateQuotaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCreateRoleStatement) {
            return new ShowCreateRoleStatementContext((ShowCreateRoleStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCreateRowPolicyStatement) {
            return new ShowCreateRowPolicyStatementContext((ShowCreateRowPolicyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCreateUserStatement) {
            return new ShowCreateUserStatementContext((ShowCreateUserStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowDictionariesStatement) {
            return new ShowDictionariesStatementContext((ShowDictionariesStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowProcessListStatement) {
            return new ShowProcessListStatementContext((ShowProcessListStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SystemStatement) {
            return new SystemStatementContext((SystemStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterClientSideEncryptionStatement) {
            return new AlterClientSideEncryptionStatementContext((AlterClientSideEncryptionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterPseStatement) {
            return new AlterPseStatementContext((AlterPseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreateClientSideEncryptionStatement) {
            return new CreateClientSideEncryptionStatementContext((CreateClientSideEncryptionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CreatePseStatement) {
            return new CreatePseStatementContext((CreatePseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropClientSideEncryptionStatement) {
            return new DropClientSideEncryptionStatementContext((DropClientSideEncryptionStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPseStatement) {
            return new DropPseStatementContext((DropPseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof DropPublicKeyStatement) {
            return new DropPublicKeyStatementContext((DropPublicKeyStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ExportStatement) {
            return new ExportStatementContext((ExportStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof RecoverStatement) {
            return new RecoverStatementContext((RecoverStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetPseStatement) {
            return new SetPseStatementContext((SetPseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof SetSchemaStatement) {
            return new SetSchemaStatementContext((SetSchemaStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UnsetPseStatement) {
            return new UnsetPseStatementContext((UnsetPseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UnsetStatement) {
            return new UnsetStatementContext((UnsetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UnsetSystemLicenseStatement) {
            return new UnsetSystemLicenseStatementContext((UnsetSystemLicenseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof UpdateStatisticsStatement) {
            return new UpdateStatisticsStatementContext((UpdateStatisticsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof CloseDatabaseStatement) {
            return new CloseDatabaseStatementContext((CloseDatabaseStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof AlterResourceGroupStatement) {
            return new AlterResourceGroupStatementContext((AlterResourceGroupStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCharacterSetStatement) {
            return new ShowCharacterSetStatementContext((ShowCharacterSetStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowCollationStatement) {
            return new ShowCollationStatementContext((ShowCollationStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowBinaryLogsStatement) {
            return new ShowBinaryLogsStatementContext((ShowBinaryLogsStatement) sqlStatement, currentDatabaseName);
        }
        if (sqlStatement instanceof ShowBinlogEventsStatement) {
            return new ShowBinlogEventsStatementContext((ShowBinlogEventsStatement) sqlStatement, currentDatabaseName);
        }
        return new UnknownSQLStatementContext(sqlStatement);
    }
}
