package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowProfilesStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show profiles statement context.
 */
@Getter
public final class ShowProfilesStatementContext extends CommonSQLStatementContext {

    public ShowProfilesStatementContext(final ShowProfilesStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show profiles statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowProfilesStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有PROFILE常量，创建变量用于引用
        String profileType = "PROFILE";
        sqlAuthModel.setType(profileType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowProfilesStatement getSqlStatement() {
        return (ShowProfilesStatement) super.getSqlStatement();
    }
}
