package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCreateTriggerStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show create trigger statement context.
 */
@Getter
public final class ShowCreateTriggerStatementContext extends CommonSQLStatementContext {

    public ShowCreateTriggerStatementContext(final ShowCreateTriggerStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show create trigger statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCreateTriggerStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_TRIGGER);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCreateTriggerStatement getSqlStatement() {
        return (ShowCreateTriggerStatement) super.getSqlStatement();
    }
}
