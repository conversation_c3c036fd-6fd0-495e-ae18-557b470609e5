package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowFunctionStatusStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show function status statement context.
 */
@Getter
public final class ShowFunctionStatusStatementContext extends CommonSQLStatementContext {

    public ShowFunctionStatusStatementContext(final ShowFunctionStatusStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show function status statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowFunctionStatusStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowFunctionStatusStatement getSqlStatement() {
        return (ShowFunctionStatusStatement) super.getSqlStatement();
    }
}
