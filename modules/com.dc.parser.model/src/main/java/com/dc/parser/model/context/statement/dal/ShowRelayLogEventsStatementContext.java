package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowRelayLogEventsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show relay log events statement context.
 */
@Getter
public final class ShowRelayLogEventsStatementContext extends CommonSQLStatementContext {

    public ShowRelayLogEventsStatementContext(final ShowRelayLogEventsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show relay log events statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowRelayLogEventsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有RELAY_LOG常量，创建变量用于引用
        String relayLogType = "RELAY_LOG";
        sqlAuthModel.setType(relayLogType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getLogName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowRelayLogEventsStatement getSqlStatement() {
        return (ShowRelayLogEventsStatement) super.getSqlStatement();
    }
}
