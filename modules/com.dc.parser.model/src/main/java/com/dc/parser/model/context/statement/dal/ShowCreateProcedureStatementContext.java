package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCreateProcedureStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show create procedure statement context.
 */
@Getter
public final class ShowCreateProcedureStatementContext extends CommonSQLStatementContext {

    public ShowCreateProcedureStatementContext(final ShowCreateProcedureStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show create procedure statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCreateProcedureStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.PROCEDURE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getProcedureName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCreateProcedureStatement getSqlStatement() {
        return (ShowCreateProcedureStatement) super.getSqlStatement();
    }
}
