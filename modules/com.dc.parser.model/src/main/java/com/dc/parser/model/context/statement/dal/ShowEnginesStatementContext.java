package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowEnginesStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show engines statement context.
 */
@Getter
public final class ShowEnginesStatementContext extends CommonSQLStatementContext {

    public ShowEnginesStatementContext(final ShowEnginesStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show engines statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowEnginesStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有ENGINES常量，创建变量用于引用
        String enginesType = "ENGINES";
        sqlAuthModel.setType(enginesType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowEnginesStatement getSqlStatement() {
        return (ShowEnginesStatement) super.getSqlStatement();
    }
}
