package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowReplicaStatusStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show replica status statement context.
 */
@Getter
public final class ShowReplicaStatusStatementContext extends CommonSQLStatementContext {

    public ShowReplicaStatusStatementContext(final ShowReplicaStatusStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show replica status statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowReplicaStatusStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有REPLICA_STATUS常量，创建变量用于引用
        String replicaStatusType = "REPLICA_STATUS";
        sqlAuthModel.setType(replicaStatusType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getChannel());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowReplicaStatusStatement getSqlStatement() {
        return (ShowReplicaStatusStatement) super.getSqlStatement();
    }
}
