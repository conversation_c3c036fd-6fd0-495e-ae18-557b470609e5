package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowPluginsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show plugins statement context.
 */
@Getter
public final class ShowPluginsStatementContext extends CommonSQLStatementContext {

    public ShowPluginsStatementContext(final ShowPluginsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show plugins statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowPluginsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有PLUGIN常量，创建变量用于引用
        String pluginType = "PLUGIN";
        sqlAuthModel.setType(pluginType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowPluginsStatement getSqlStatement() {
        return (ShowPluginsStatement) super.getSqlStatement();
    }
}
