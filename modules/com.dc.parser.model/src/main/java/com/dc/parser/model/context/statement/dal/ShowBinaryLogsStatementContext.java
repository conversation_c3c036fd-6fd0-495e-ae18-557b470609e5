package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowBinaryLogsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show binary logs statement context.
 */
@Getter
public final class ShowBinaryLogsStatementContext extends CommonSQLStatementContext {

    public ShowBinaryLogsStatementContext(final ShowBinaryLogsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show binary logs statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowBinaryLogsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有BINARY_LOGS常量，创建变量用于引用
        String binaryLogsType = "BINARY_LOGS";
        sqlAuthModel.setType(binaryLogsType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowBinaryLogsStatement getSqlStatement() {
        return (ShowBinaryLogsStatement) super.getSqlStatement();
    }
}
