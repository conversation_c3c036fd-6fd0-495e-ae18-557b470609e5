package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowVariablesStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show variables statement context.
 */
@Getter
public final class ShowVariablesStatementContext extends CommonSQLStatementContext {

    public ShowVariablesStatementContext(final ShowVariablesStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show variables statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowVariablesStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_VARIABLE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowVariablesStatement getSqlStatement() {
        return (ShowVariablesStatement) super.getSqlStatement();
    }
}
