package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowEventsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show events statement context.
 */
@Getter
public final class ShowEventsStatementContext extends CommonSQLStatementContext {

    public ShowEventsStatementContext(final ShowEventsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show events statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowEventsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有EVENT常量，创建变量用于引用
        String eventType = "EVENT";
        sqlAuthModel.setType(eventType);
        
        // 如果指定了数据库，使用指定的数据库名，否则使用当前数据库名
        String schemaName = currentDatabaseName;
        if (sqlStatement.getFromDatabase().isPresent()) {
            schemaName = sqlStatement.getFromDatabase().get().getDatabase().getIdentifier().getValue();
        }
        sqlAuthModel.setSchemaName(schemaName);
        
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowEventsStatement getSqlStatement() {
        return (ShowEventsStatement) super.getSqlStatement();
    }
}
