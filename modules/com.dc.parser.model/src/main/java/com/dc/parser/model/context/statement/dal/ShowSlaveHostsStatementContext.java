package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowSlaveHostsStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show slave hosts statement context.
 */
@Getter
public final class ShowSlaveHostsStatementContext extends CommonSQLStatementContext {

    public ShowSlaveHostsStatementContext(final ShowSlaveHostsStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show slave hosts statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowSlaveHostsStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有SLAVE_HOSTS常量，创建变量用于引用
        String slaveHostsType = "SLAVE_HOSTS";
        sqlAuthModel.setType(slaveHostsType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowSlaveHostsStatement getSqlStatement() {
        return (ShowSlaveHostsStatement) super.getSqlStatement();
    }
}
