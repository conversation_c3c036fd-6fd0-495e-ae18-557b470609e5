package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCharsetStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show charset statement context.
 */
@Getter
public final class ShowCharsetStatementContext extends CommonSQLStatementContext {

    public ShowCharsetStatementContext(final ShowCharsetStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show charset statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCharsetStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有CHARSET常量，创建变量用于引用
        String charsetType = "CHARSET";
        sqlAuthModel.setType(charsetType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCharsetStatement getSqlStatement() {
        return (ShowCharsetStatement) super.getSqlStatement();
    }
}
