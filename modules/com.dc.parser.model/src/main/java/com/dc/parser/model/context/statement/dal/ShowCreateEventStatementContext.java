package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCreateEventStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Show create event statement context.
 */
@Getter
public final class ShowCreateEventStatementContext extends CommonSQLStatementContext {

    public ShowCreateEventStatementContext(final ShowCreateEventStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    /**
     * Extract SQL auth model.
     *
     * @param sqlStatement        show create event statement
     * @param currentDatabaseName current database name
     */
    public void extractSqlAuthModel(final ShowCreateEventStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        // 由于SqlConstant中没有EVENT常量，创建变量用于引用
        String eventType = "EVENT";
        sqlAuthModel.setType(eventType);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getEventName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCreateEventStatement getSqlStatement() {
        return (ShowCreateEventStatement) super.getSqlStatement();
    }
}
