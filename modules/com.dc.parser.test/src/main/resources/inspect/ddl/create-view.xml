<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sql-inspects>
    <sql-inspect sql-case-id="create_view_with_udf">
        <result-set-meta-data/>
        <sql-parser-data>
            <token name="V_T1_C" operation="CREATE" schema="mock" type="view"/>
            <action isDefaultSchema="true"/>
        </sql-parser-data>
    </sql-inspect>
    <sql-inspect sql-case-id="create_view_with_udf_arg">
        <result-set-meta-data/>
        <sql-parser-data>
            <token name="V_T1_C" operation="CREATE" schema="mock" type="view"/>
            <action isDefaultSchema="true"/>
        </sql-parser-data>
    </sql-inspect>
    <sql-inspect sql-case-id="create_view">
        <result-set-meta-data/>
        <sql-parser-data>
            <token name="comedies" operation="CREATE" schema="mock" type="view"/>
            <token name="films" operation="SELECT" schema="mock" type="TABLE"/>
            <action isDefaultSchema="true"/>
        </sql-parser-data>
    </sql-inspect>
    <sql-inspect sql-case-id="create_view_with_udf_nested_fun">
        <result-set-meta-data/>
        <sql-parser-data>
            <token name="V_T1_C" operation="CREATE" schema="mock" type="view"/>
            <action isDefaultSchema="true"/>
        </sql-parser-data>
    </sql-inspect>
</sql-inspects>
