package com.dc.summer.exec.model.observer;

import com.dc.summer.exec.monitor.data.MonitorParam;
import com.dc.summer.exec.monitor.data.MonitorResult;
import com.dc.summer.exec.monitor.AbstractStatMonitor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Function;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MonitorSubject {

    private static final Set<MonitorObserver> MONITOR_OBSERVERS = new HashSet<>();

    public static void attach(MonitorObserver observer) {
        MONITOR_OBSERVERS.add(observer);
    }

    public static void remove(MonitorObserver observer) {
        MONITOR_OBSERVERS.remove(observer);
    }

    public static void transit(AbstractStatMonitor<?> abstractStatMonitor, Function<MonitorParam, MonitorResult> function) {
        MONITOR_OBSERVERS.forEach(monitorObserver -> {
            Set<Class<? extends AbstractStatMonitor<?>>> affectClassSet = monitorObserver.affectClassSet();
            if (affectClassSet != null && affectClassSet.contains(abstractStatMonitor.getClass())) {
                monitorObserver.transitResult(abstractStatMonitor.getName(), function.apply(monitorObserver.getMonitorParam()));
            }
        });
    }

}
