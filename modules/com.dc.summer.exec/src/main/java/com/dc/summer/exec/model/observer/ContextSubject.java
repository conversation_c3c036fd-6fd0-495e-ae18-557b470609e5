package com.dc.summer.exec.model.observer;


import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Consumer;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ContextSubject {

    private static final Set<ContextObserver> contextObservers = new HashSet<>();

    public static void register(ContextObserver observer) {
        contextObservers.add(observer);
    }

    public static void trigger(Consumer<ContextObserver> consumer) {
        contextObservers.forEach(consumer);
    }


}
