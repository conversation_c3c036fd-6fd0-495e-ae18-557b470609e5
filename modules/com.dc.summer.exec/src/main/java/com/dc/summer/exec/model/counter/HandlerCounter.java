package com.dc.summer.exec.model.counter;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.slf4j.Marker;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HandlerCounter {

    private static final ThreadLocal<String> SQL_THREAD_LOCAL = new ThreadLocal<>();

    private static final ThreadLocal<String> TOKEN_THREAD_LOCAL = new ThreadLocal<>();

    private static final ThreadLocal<String> CONTAINER_ID_THREAD_LOCAL = new ThreadLocal<>();

    private static final ThreadLocal<String> EXECUTE_ID_THREAD_LOCAL = new ThreadLocal<>();

    private static final ThreadLocal<String> OPERATOR_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<String> USER_ID_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<Integer> CONNECTION_PATTERN_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<Marker> MARKER_THREAD_LOCAL = new ThreadLocal<>();

    public static String getSql() {
        return SQL_THREAD_LOCAL.get();
    }

    public static String getToken() {
        return TOKEN_THREAD_LOCAL.get();
    }

    public static String getContainerId() {
        return CONTAINER_ID_THREAD_LOCAL.get();
    }

    public static String getExecuteId() {
        return EXECUTE_ID_THREAD_LOCAL.get();
    }

    public static String getOperator() {
        return OPERATOR_THREAD_LOCAL.get();
    }

    public static String getUserId() {
        return USER_ID_THREAD_LOCAL.get();
    }

    public static Integer getConnectionPattern() {
        return CONNECTION_PATTERN_THREAD_LOCAL.get();
    }

    public static Marker getMarker() {
        return MARKER_THREAD_LOCAL.get();
    }

    public static void setSql(String sql) {
        SQL_THREAD_LOCAL.set(sql);
    }

    public static void setContainerId(String containerId) {
        CONTAINER_ID_THREAD_LOCAL.set(containerId);
    }

    public static void setExecuteId(String executeId) {
        EXECUTE_ID_THREAD_LOCAL.set(executeId);
    }

    public static void setToken(String token) {
        TOKEN_THREAD_LOCAL.set(token);
    }

    public static void setOperator(String operator) {
        OPERATOR_THREAD_LOCAL.set(operator);
    }

    public static void setUserId(String userId) {
        USER_ID_THREAD_LOCAL.set(userId);
    }

    public static void setConnectionPattern(Integer connectionPattern) {
        CONNECTION_PATTERN_THREAD_LOCAL.set(connectionPattern);
    }

    public static void setMarker(Marker marker) {
        MARKER_THREAD_LOCAL.set(marker);
    }

    public static void release() {
        SQL_THREAD_LOCAL.remove();
        TOKEN_THREAD_LOCAL.remove();
        CONTAINER_ID_THREAD_LOCAL.remove();
        EXECUTE_ID_THREAD_LOCAL.remove();
        OPERATOR_THREAD_LOCAL.remove();
        USER_ID_THREAD_LOCAL.remove();
        CONNECTION_PATTERN_THREAD_LOCAL.remove();
        MARKER_THREAD_LOCAL.remove();
    }
}
