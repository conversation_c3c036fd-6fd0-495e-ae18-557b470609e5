package com.dc.summer.exec.model.proxy.impl;

import com.dc.summer.exec.model.ConnectionManager;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.handler.StatementHandler;
import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.exec.model.type.RecordSignType;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.proxy.DBPHandlerProxy;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.utils.LoggerUtils;
import com.dc.utils.StringUtils;
import org.eclipse.core.runtime.IAdaptable;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Set;

public class StatementProxy<T> extends DBPHandlerProxy<T> {

    private final SessionProxy sessionProxy;

    protected boolean activate;

    private static final Set<Class<?>> CLASS_SET = Set.of(DBCStatement.class, Statement.class);

    private static final Set<String> EXECUTE_SET = Set.of("execute", "executeBatch",
            "executeUpdate", "executeLargeBatch", "executeLargeUpdate",
            "executeStatement", "executeStatementBatch", "executeQuery");

    private static final Set<String> RENEW_SET = Set.of("getUpdateCount", "getLargeUpdateCount",
            "getUpdateRowCount", "executeUpdate", "executeLargeUpdate");

    @Override
    public Set<Class<?>> getClassSet() {
        return CLASS_SET;
    }

    public StatementProxy(T t, SessionProxy sessionProxy, boolean activate) {
        super(t);
        this.sessionProxy = sessionProxy;
        this.activate = activate;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {

        String methodName = method.getName();

        RecordHandler sqlHandle = RecordHandler.handle(RecordType.SQL, activate);
        RecordHandler sessionHandle = RecordHandler.handle(RecordType.SESSION, activate);
        RecordHandler dataSourceHandle = RecordHandler.handle(RecordType.DATASOURCE, activate);
        StatParam param = new StatParam();
        String token = HandlerCounter.getToken();
        param.setToken(token);
        param.setMethodName(methodName);
        param.setContainerId(HandlerCounter.getContainerId());
        param.setSql(HandlerCounter.getSql());
        param.setOperator(HandlerCounter.getOperator());
        param.setUserId(HandlerCounter.getUserId());
        param.setConnectionPattern(HandlerCounter.getConnectionPattern());
        StatementHandler statementHandler = StatementHandler.handle(param.getToken());

        long currentTimeMillis = System.currentTimeMillis();

        boolean isExecute = EXECUTE_SET.contains(methodName);
        boolean isRenew = RENEW_SET.contains(methodName);
        boolean isCommit = SQLConstants.KEYWORD_COMMIT.equalsIgnoreCase(methodName);
        boolean isRollback = SQLConstants.KEYWORD_ROLLBACK.equalsIgnoreCase(methodName);
        boolean isStatement = getT() instanceof Statement;

        String processId = "";
        ConnectionManager connectionManager = null;
        try {
            if (isExecute) {
                if (this.sessionProxy.getT() != null) {
                    processId = this.sessionProxy.getT().getExecutionContext().getProcessId();
                    DBPDataSourceContainer container = this.sessionProxy.getT().getExecutionContext().getDataSource().getContainer();
                    if (container instanceof IAdaptable) {
                        connectionManager = ((IAdaptable) container).getAdapter(ConnectionManager.class);
                    }
                }
                if (args != null) {
                    for (Object arg : args) {
                        if (arg instanceof String) {
                            HandlerCounter.setSql((String) arg);
                            param.setSql((String) arg);
                        }
                    }
                }
                if (isStatement) {
                    statementHandler.login((Statement) getT(), processId);
                }
                sqlHandle.appendRow(param, RecordSignType.EXECUTE_START);
                sessionHandle.appendRow(param, RecordSignType.EXECUTE_START);
                dataSourceHandle.appendRow(param, RecordSignType.EXECUTE_START);
            }

            Object result = method.invoke(getT(), args);
            loggingArgs(methodName, args, result);

            if (isCommit) {
                if (Boolean.FALSE.equals(isAutoCommit())) {
                    sqlHandle.appendRow(param, RecordSignType.EXECUTE_COMMIT);
                    sessionHandle.appendRow(param, RecordSignType.EXECUTE_COMMIT);
                    dataSourceHandle.appendRow(param, RecordSignType.EXECUTE_COMMIT);
                }
            }
            if (isRollback) {
                if (Boolean.FALSE.equals(isAutoCommit())) {
                    sqlHandle.appendRow(param, RecordSignType.EXECUTE_ROLLBACK);
                    sessionHandle.appendRow(param, RecordSignType.EXECUTE_ROLLBACK);
                    dataSourceHandle.appendRow(param, RecordSignType.EXECUTE_ROLLBACK);
                }
            }
            if (isRenew) {
                long row = Long.parseLong(String.valueOf(result));
                if (row > 0) {
                    sqlHandle.appendRow(param, RecordSignType.RENEW_START);
                    sessionHandle.appendRow(param, RecordSignType.RENEW_START);
                    param.setRow(row);
                    sqlHandle.appendRow(param, RecordSignType.RENEW_OVER);
                    sessionHandle.appendRow(param, RecordSignType.RENEW_OVER);
                }
            }
            if (isExecute) {
                if (isStatement) {
                    statementHandler.logout((Statement) getT(), processId);
                    if (connectionManager != null) {
                        connectionManager.refreshTime(token);
                    }
                }
                if (Boolean.TRUE.equals(isAutoCommit())) {
                    sqlHandle.appendRow(param, RecordSignType.EXECUTE_COMMIT);
                    sessionHandle.appendRow(param, RecordSignType.EXECUTE_COMMIT);
                    dataSourceHandle.appendRow(param, RecordSignType.EXECUTE_COMMIT);
                }
                sqlHandle.appendRow(param, RecordSignType.EXECUTE_OVER);
                sessionHandle.appendRow(param, RecordSignType.EXECUTE_OVER);
                dataSourceHandle.appendRow(param, RecordSignType.EXECUTE_OVER);
            }
            /*ResultSetProxy<?> resultProxy = new ResultSetProxy<>(result);
            if (resultProxy.isMatching()) {
                result = resultProxy.getInstance();
            }*/
            return result;
        } catch (IllegalAccessException | InvocationTargetException e) {
            if (isExecute) {
                if (isStatement) {
                    statementHandler.logout((Statement) getT(), processId);
                    if (connectionManager != null) {
                        connectionManager.refreshTime(token);
                    }
                }
                sqlHandle.appendRow(param, RecordSignType.EXECUTE_ERROR);
                sessionHandle.appendRow(param, RecordSignType.EXECUTE_ERROR);
                dataSourceHandle.appendRow(param, RecordSignType.EXECUTE_ERROR);
            }
            throw e.getCause();
        } finally {
            loggingTime(methodName, currentTimeMillis);
        }
    }

    private void loggingArgs(String methodName, Object[] args, Object result) {
        try {
            boolean isSet = methodName.startsWith("set") && !methodName.equals("setStatementSource");
            boolean isExecute = methodName.startsWith("execute") && args != null && args.length > 0;
            boolean isUpdates = methodName.equals("getUpdateRowCount") && result instanceof Long;
            String methodNameUpper = StringUtils.firstUpper(methodName);
            if (isExecute) {
                for (Object arg : args) {
                    if (arg instanceof String) {
                        LoggerUtils.print(HandlerCounter.getMarker(), "\n==> {}:\n{}", methodNameUpper, arg);
                    }
                }
            } else if (isSet) {
                LoggerUtils.print(HandlerCounter.getMarker(), "\n==> {}:\n{}", methodNameUpper, Arrays.toString(args));
            } else if (isUpdates) {
                LoggerUtils.print(HandlerCounter.getMarker(), "\n<== {}:\n{}", "Updates", result);
            }
        } catch (Exception ignored) {
        }
    }

    private void loggingTime(String methodName, long time) {
        try {
            boolean isExecute = methodName.startsWith("execute");
            if (isExecute) {
                LoggerUtils.print(HandlerCounter.getMarker(), "\n<== {}:\n{} ms", "ExecuteTime", System.currentTimeMillis() - time);
            }
        } catch (Exception ignored) {
        }
    }

    private Boolean isAutoCommit() {
        if (sessionProxy.getT() instanceof JDBCSession) {
            try {
                return ((JDBCSession) sessionProxy.getT()).getAutoCommit();
            } catch (SQLException ignored) {
            }
        }
        return true;
    }
}
