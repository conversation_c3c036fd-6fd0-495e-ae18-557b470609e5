package com.dc.summer.exec.model.proxy.impl;

import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.model.proxy.DBPHandlerProxy;
import com.dc.summer.exec.model.type.RecordSignType;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.model.exec.DBCResultSet;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.ResultSet;
import java.util.Set;

@Deprecated
@Slf4j
public class ResultSetProxy<T> extends DBPHandlerProxy<T> {

    private static final Set<Class<?>> CLASS_SET = Set.of(DBCResultSet.class, ResultSet.class);
    private static final Set<String> NEXT_SET = Set.of("nextRow", "next");

    private long index = 0L;

    public ResultSetProxy(T t) {
        super(t);
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {

        String methodName = method.getName();

        try {
            Object result = method.invoke(getT(), args);
            logging(methodName, args, result);

            if (NEXT_SET.contains(methodName)) {
                RecordHandler sqlHandle = RecordHandler.handle(RecordType.SQL);
                RecordHandler sessionHandle = RecordHandler.handle(RecordType.SESSION);
                StatParam param = new StatParam();
                param.setToken(HandlerCounter.getToken());
                param.setSql(HandlerCounter.getSql());
                if (index == 0) {
                    sqlHandle.appendRow(param, RecordSignType.READ_START);
                    sessionHandle.appendRow(param, RecordSignType.READ_START);
                }
                if (Boolean.TRUE.equals(result)) {
                    index++;
                } else {
                    param.setRow(index);
                    sqlHandle.appendRow(param, RecordSignType.READ_OVER);
                    sessionHandle.appendRow(param, RecordSignType.READ_OVER);
                }
            }

            return result;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw e.getCause();
        }
    }

    private void logging(String methodName, Object[] args, Object result) {
        try {
            if (NEXT_SET.contains(methodName) && result instanceof Boolean && !((boolean) result)) {
                log.info("\n<== {}:\n{}", "Total", index);
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public Set<Class<?>> getClassSet() {
        return CLASS_SET;
    }
}
